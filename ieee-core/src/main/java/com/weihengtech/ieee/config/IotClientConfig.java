package com.weihengtech.ieee.config;

import com.weihengtech.ieee.service.passthrough.IotClientService;
import com.weihengtech.ieee.service.passthrough.impl.IotClientServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * iot client配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/9 9:49
 */
@Configuration
public class IotClientConfig {

    private String elinkCloud = "0";
    private String tuyaCloud = "1";
    private String ocppCloud = "2";
    private String whCloud = "3";

    @Bean("tuyaIotClient")
    IotClientService tuyaIotClient() {
        return new IotClientServiceImpl(tuyaCloud);
    }

    @Bean("eLinkIotClient")
    IotClientService eLinkIotClient() {
        return new IotClientServiceImpl(elinkCloud);
    }

    @Bean("ocppIotClient")
    IotClientService ocppIotClient() {
        return new IotClientServiceImpl(ocppCloud);
    }

    @Bean("whIotClient")
    IotClientService whIotClient() {
        return new IotClientServiceImpl(whCloud);
    }
}
